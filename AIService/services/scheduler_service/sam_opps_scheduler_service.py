import asyncio
from typing import Any, Dict, List, Callable, Awaitable

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from services.proposal.key_personnel import KeyPersonnelService
from database import get_kontratar_db
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from services.data_load.process_document import DocumentProcessingService
from controllers.kontratar.sam_opportunity_queue_controller import SamOpport<PERSON><PERSON>ueueController
from controllers.kontratar.opportunity_table_info_controller import OpportunityTableInfoController
from services.proposal.technical_requirements import TechnicalRequirementsService
from controllers.kontratar.sam_opps_table_coontroller import <PERSON><PERSON><PERSON><PERSON>roller
import json
from services.proposal.formatting_requirements import FormattingRequirementsService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.outline import ProposalOutlineService
from services.proposal.utilities import ProposalUtilities
from services.proposal.summary import SummaryService
from services.proposal.search_queries import ExtractKeywords
from sqlalchemy import select, text
from models.kontratar_models import OppsTable
import binascii
from services.scheduler_service.schedule_lock_service import ScheduleLockService
from utils.semaphore import run_with_semaphore
from controllers.customer.proposal_outline_queue_controller import ProposalOutlineQueueController
import ast


class SamOppsSchedulerService:
    """Scheduler service for processing SAM opportunity queue items"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False
        self.tech_requirements_service = TechnicalRequirementsService()
        self.formatting_requirements_service = FormattingRequirementsService()
        self.content_compliance_service = ContentComplianceService()
        self.structure_compliance_service = StructureComplianceService()
        self.proposal_outline_service = ProposalOutlineService()
        self.summary_service = SummaryService()
        self.keywords_service = ExtractKeywords()
        self.lock_service = ScheduleLockService("SAM_OPPS_SCHEDULER_LOCK", "SAM_OPPS_SCHEDULER")
    
       
    async def process_sam_opps_queue(self):
        """Process new SAM opportunity queue items"""
        if not self.is_enabled:
            logger.info("SAM opps scheduler is disabled, skipping processing")
            return
        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("SAM opps scheduler lock not acquired, skipping this run")
            return
        
        CONCURRENCY_LIMIT = 2
        MAX_ITEMS_PER_RUN = 10
        
        try:

            async for db in get_kontratar_db():
                items = await SamOpportunityQueueController.claim_new_queue_items(
                    db, limit=MAX_ITEMS_PER_RUN
                )
                break

            if not items:
                logger.info(f"No more items to process.")
                return

        
            # Process items with a semaphore so new jobs start as soon as a slot frees up
            async def _on_enter(item):
                await self.update_batch_status([str(item.opps_id)], "PROCESSING")

            async def worker(item):
                try:
                    await asyncio.wait_for(
                        self._process_sam_opps_item(item), timeout=3600  # 1 hour timeout
                    )
                    await self.update_batch_status([str(item.opps_id)], "COMPLETED")
                    logger.info(f"Completed item {item.opps_id}")
                    async for db in get_kontratar_db():
                        await ProposalOutlineQueueController.add_to_queue_from_worker(
                            db=db,
                            opps_id=item.opps_id,
                            tenant_id="SYSTEM",
                            outline_type="sam",
                            first_request=True
                        )
                        break   
                except asyncio.TimeoutError:
                    logger.error(f"Timeout on item {item.opps_id}")
                    await self.update_batch_status([str(item.opps_id)], "AI_TIMEOUT")
                except Exception as e:
                    logger.error(f"Error on item {item.opps_id}: {e}")
                    await self.update_batch_status([str(item.opps_id)], "FAILED")

            await run_with_semaphore(
                items=items,
                max_jobs=CONCURRENCY_LIMIT,
                on_enter=_on_enter,
                worker=worker,
            )
        finally:
            await self.lock_service.release_lock()
            
    async def update_batch_status(self, opps_ids: List[str], status: str):
        async for db in get_kontratar_db():
            await SamOpportunityQueueController.update_queue_status_batch(
                db, opps_ids, status
            )
            break
    
    async def _process_sam_opps_item(self, item):
        """Process a SAM opps queue item"""
        logger.info(f"Processing SAM opps item: {item.opps_id}")
        
        # Get all corresponding OpportunityTableInfo records
        async for db in get_kontratar_db():
            opp_infos = await OpportunityTableInfoController.get_all_by_opps_id(item.opps_id, db)
            break
        if not opp_infos:
            logger.warning(f"No OpportunityTableInfo found for opps_id={item.opps_id}")
            return
        
        all_chunks = []
        for opp_info in opp_infos:
            raw_text = opp_info.opps_raw_text

            logger.debug(f"Processing raw text {raw_text[:100]}... for opps_id={item.opps_id}")
      
            # Handle bytes or hex-encoded bytes
            # if isinstance(raw_text, bytes):
            #     try:
            #         plain_text = raw_text.decode("utf-8")
            #     except Exception:
            #         plain_text = raw_text.decode("latin1", errors="replace")
            # elif isinstance(raw_text, str):
            #     # Try to decode hex if it looks like a hex string
            #     try:
            #         if all(c in '0123456789abcdefABCDEF' for c in raw_text.strip()[:32]) and len(raw_text) % 2 == 0:
            #             plain_text = bytes.fromhex(raw_text).decode("utf-8", errors="replace")
            #         else:
            #             plain_text = raw_text
            #     except Exception:
            #         plain_text = raw_text
            # else:
            #     logger.warning(f"Unknown type for opps_raw_text: {type(raw_text)}")
            #     plain_text = str(raw_text)
            plain_text = self.decode_possible_hex(raw_text)
            processed = DocumentProcessingService.process_corpus(plain_text)
            chunks = DocumentProcessingService.semantic_chunk(processed)
            all_chunks.extend(chunks)
        collection_name = item.opps_id
        await DocumentProcessingService.add_documents(collection_name, all_chunks)
        logger.info(f"Completed processing SAM opps item: {item.opps_id}")

        ## Summary and other prelimniary AI generation will be done here
        # Define the operations in order

        # Generate summary
        await self._generate_summary(item)

        # Generate technical requirements
        await self._generate_technical_requirements(item)

        # Generate formatting requirements
        await self._generate_formatting_requirements(item)

        # Generate content compliance
        await self._generate_content_compliance(item)

        # Generate structure compliance
        await self._generate_structure_compliance(item)

        # Generate table of contents
        await self._generate_table_of_contents(item)

        # # Generate proposal outline
        # await self._generate_proposal_outline(item)

        # Generate keywords
        await self._generate_keywords(item)

        # Generate key personnel titles
        await self._generate_key_personnel_titles(item)
    
        
    def decode_possible_hex(self, raw_text: str) -> str:
        if isinstance(raw_text, bytes):
            try:
                logger.debug("Decoding bytes to string")
                
                return raw_text.decode("utf-8")
            except Exception:
                return raw_text.decode("latin1", errors="replace")
        if isinstance(raw_text, str):
            # Handle \x hex escapes
            if raw_text.startswith("\\x"):
                try:
                    logger.debug("Decoding hex-encoded string")
                    
                    clean_hex = raw_text.replace('\\x', '')

                    byte_data = binascii.unhexlify(clean_hex)
                    decoded =  byte_data.decode('utf-8')
                    logger.debug(f"Decoded raw text (first 100 chars): {decoded[:100]}...")
                    return decoded         
                
                except (binascii.Error, UnicodeDecodeError) as e:
                    logger.error(f"Failed to decode raw text for opportunity: {e}")
    
                    return raw_text
            # Handle plain hex string
            try:
                if all(c in '0123456789abcdefABCDEF' for c in raw_text.strip()[:32]) and len(raw_text) % 2 == 0:
                    return bytes.fromhex(raw_text).decode("utf-8", errors="replace")
            except Exception:
                pass
            return raw_text
     
        return str(raw_text)    
    
    async def _is_rfp_opportunity(self, db: AsyncSession, opportunity_id: str) -> bool:
        """
        Check if the opportunity is an RFP based on type_op or similar field in OppsTable.
        Returns True if it's an RFP, False if it's an RFI.
        """
        try:
            query = select(OppsTable.type_op).where(OppsTable.notice_id == opportunity_id)
            result = await db.execute(query)
            opportunity_type = result.scalar_one_or_none()

            if not opportunity_type:
                logger.warning(f"No type_op found for opportunity_id: {opportunity_id}, defaulting to RFI")
                return False

            opp_type_lower = opportunity_type.lower()
            rfp_types = [
                "combined synopsis",
                "solicitation",
                "combined synopsis/solicitation"
            ]
            is_rfp_by_type = any(rfp_type in opp_type_lower for rfp_type in rfp_types)
            is_rfp_by_name = "request for proposal" in opp_type_lower or "rfp" in opp_type_lower

            is_rfp = is_rfp_by_type or is_rfp_by_name

            logger.info(f"Opportunity {opportunity_id} type: '{opportunity_type}' - Classified as: {'RFP' if is_rfp else 'RFI'}")
            return is_rfp

        except Exception as e:
            logger.error(f"Error checking RFP status for opportunity {opportunity_id}: {e}")
            return False
        
    async def _generate_keywords(self, item):
        """Generate and save keyword search queries for the SAM opportunity"""
        logger.info(f"Generating keyword search queries for SAM opportunity: {item.opps_id}")
        try:
            # Get all corresponding OpportunityTableInfo records
            async for db in get_kontratar_db():
                
                opp_infos = await OpportunityTableInfoController.get_all_by_opps_id(item.opps_id, db)
                break
                    
            if not opp_infos:
                logger.warning(f"No OpportunityTableInfo found for opps_id={item.opps_id}")
                return

            # Combine all plain texts into one string for LLM context
            texts = []
            for opp_info in opp_infos:
                raw_text = opp_info.opps_raw_text
                if isinstance(raw_text, bytes):
                    try:
                        plain_text = raw_text.decode("utf-8")
                    except Exception:
                        plain_text = raw_text.decode("latin1", errors="replace")
                elif isinstance(raw_text, str):
                    try:
                        logger.debug("Decoding hex-encoded string")
                    
                        clean_hex = raw_text.replace('\\x', '')

                        byte_data = binascii.unhexlify(clean_hex)
                        plain_text =  byte_data.decode('utf-8')
                        logger.debug(f"Decoded raw text (first 100 chars): {plain_text[:100]}...")
                    

                    except (binascii.Error, UnicodeDecodeError) as e:
                        logger.error(f"Failed to decode raw text for opps_id={item.opps_id}: {e}")
    
                        return raw_text
                else:
                    plain_text = str(raw_text)
                texts.append(plain_text)
            combined_text = "\n\n".join(texts)
            logger.debug(f"Combined text for keywords (first 100 chars): {combined_text[:100]}...")

            queries = await self.keywords_service.get_queries(combined_text, get_related_queries=True)
            if not queries:
                logger.warning(f"No keywords generated for SAM opportunity: {item.opps_id}")
                return

            # Save as JSON string in the keywords field
            keywords_json = json.dumps(queries, ensure_ascii=False)
            async for db in get_kontratar_db():
                updated_record = await OppsController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=item.opps_id,
                    update_fields={"keywords": keywords_json}
                )
                break  # Now the session will close
            if updated_record:
                logger.info(f"Successfully stored keywords for SAM opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update OppsTable for SAM opportunity: {item.opps_id}")
        except Exception as e:
            logger.error(f"Error generating keywords for SAM opportunity {item.opps_id}: {e}")
    
    
    async def _generate_summary(self, item):
        """Generate and save summary for the SAM opportunity"""
        logger.info(f"Generating summary for SAM opportunity: {item.opps_id}")
        try:
            async for db in get_kontratar_db():
                summary = await self.summary_service.generate_summary(
                    db=db,
                    tenant_id=getattr(item, "tenant_id", None) or "default",
                    opportunity_id=item.opps_id,
                    source=getattr(item, "opps_source", None) or "sam"
                )
                break  # Now the session will close
            if not summary:
                logger.warning(f"No summary generated for SAM opportunity: {item.opps_id}")
                return
            async for db in get_kontratar_db():
            # Save the summary using the summary service (which uses the correct controller)
                await self.summary_service.set_summary(
                    db=db,
                    tenant_id=getattr(item, "tenant_id", None) or "default",
                    opportunity_id=item.opps_id,
                    source=getattr(item, "opps_source", None) or "sam",
                    summary=summary
                )
                break
            logger.info(f"Successfully stored summary for SAM opportunity: {item.opps_id}")
        except Exception as e:
            logger.error(f"Error generating summary for SAM opportunity {item.opps_id}: {e}")
    
    
    async def _generate_proposal_outline(self, item):
        """Generate proposal outline for each TOC and store in OppsTable"""
        logger.info(f"Generating Proposal Outline for SAM opportunity: {item.opps_id}")

        try:
            # Retrieve all TOCs from DB
            query = text(
                "SELECT toc_text, toc_text2, toc_text3, toc_text4, toc_text5 "
                "FROM kontratar_main.oppstable WHERE notice_id = :opps_id"
            )
            async for db in get_kontratar_db():
                result = await db.execute(query, {"opps_id": item.opps_id})
                break
            row = result.first()
            if not row:
                logger.warning(f"No TOC found for SAM opportunity: {item.opps_id}")
                return

            toc_fields = ["toc_text", "toc_text2", "toc_text3", "toc_text4", "toc_text5"]
            outline_fields = [
            "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
            "proposal_outline_4", "proposal_outline_5"
        ]
            update_fields = {}

            for idx, toc_text in enumerate(row):
                if not toc_text:
                    continue
                try:
                    toc_json = json.loads(toc_text)
                except Exception:
                    logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for SAM opportunity: {item.opps_id}")
                    continue

                table_of_contents = toc_json
                if not table_of_contents:
                    logger.warning(f"No table_of_contents in {toc_fields[idx]} for SAM opportunity: {item.opps_id}")
                    continue
                logger.debug(f"Generating outline for TOC field {toc_fields[idx]}: {table_of_contents}")
                # Generate the outline using the ProposalOutlineService
                outline_result = await self.proposal_outline_service.generate_outline(
                    opportunity_id=item.opps_id,
                    tenant_id=getattr(item, "tenant_id", None) or "default",
                    source=getattr(item, "opps_source", None) or "sam",
                    table_of_contents=table_of_contents
                )
                
                outline_content = outline_result.get("outlines", "")
                
                
                logger.debug(f"Outline content for {toc_fields[idx]}: {str(outline_content)[:100]}...")
                if not outline_content:
                    logger.warning(f"No outline generated for TOC field {toc_fields[idx]} in SAM opportunity: {item.opps_id}")
                    continue  # Don't return, just skip this one
                
                # Store the outline JSON string in the appropriate field
                outline_content_json_str = json.dumps(outline_content, ensure_ascii=False)
                update_fields[outline_fields[idx]] = outline_content_json_str

            if update_fields:
                async for db in get_kontratar_db():
                    updated_record = await OppsController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=item.opps_id,
                        update_fields=update_fields
                    )
                    break 
                if updated_record:
                    logger.info(f"Successfully stored Proposal Outline for SAM opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update OppsTable with Proposal Outline for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Proposal Outline for SAM opportunity {item.opps_id}: {e}")
    
    
    
    async def _generate_table_of_contents(self, item):
        """Generate Table of Contents for each volume and store in OppsTable"""
        logger.info(f"Generating Table of Contents for SAM opportunity: {item.opps_id}")

        try:
            # Retrieve structure and content compliance from DB
            query = text(
                "SELECT structure_compliance, content_compliance, type_op "
                "FROM kontratar_main.oppstable WHERE notice_id = :opps_id"
            )
            async for db in get_kontratar_db():
                result = await db.execute(query, {"opps_id": item.opps_id})
                break
            row = result.first()
            if not row or not row[0]:
                logger.warning(f"No structure compliance found for opportunity: {item.opps_id}")
                return

            structure_compliance_str, content_compliance_str, opportunity_type = row
            # Parse structure compliance JSON
            structure_compliance = ast.literal_eval(structure_compliance_str)
            content_compliance = ast.literal_eval(content_compliance_str)
            if not structure_compliance or "structure" not in structure_compliance:
                logger.warning(f"Invalid structure compliance JSON for opportunity: {item.opps_id}")
                return
            
            volume_definitions = structure_compliance["structure"]

            # Split compliance data by volume
            volumes = {}

            # Extract structure data
            for volume in volume_definitions:
                volume_title = volume.get("volume_title", "")
                volumes[volume_title] = {
                    "structure": volume,
                    "content": None
                }

            # Match content compliance to volumes
            if content_compliance and "content_compliance" in content_compliance:
                logger.info("Using structured content compliance data...")
                for compliance in content_compliance["content_compliance"]:
                    volume_title = compliance.get("volume_title", "")
                    # Match volume titles
                    for vol_key in volumes.keys():
                        if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                            volumes[vol_key]["content"] = compliance
                            break

            else:
                logger.warning("No structured content compliance, will use full content for each volume")
                # Use full content compliance for each volume
                for vol_key in volumes.keys():
                    volumes[vol_key]["content"] = {"content": content_compliance}

            logger.info(f"✓ Split compliance into {len(volumes)} volumes: {list(volumes.keys())}")

            update_fields = {}
            toc_fields = [
                "toc_text", "toc_text2", "toc_text3", "toc_text4", "toc_text5"
            ]

            # Process each volume through the complete pipeline
            for idx, (volume_title, volume_data) in enumerate(volumes.items()):
                if idx >= len(toc_fields):
                    logger.warning(f"Skipping volume '{volume_title}' because there are more than {len(toc_fields)} volumes.")
                    continue

                logger.info(f"PROCESSING VOLUME: {volume_title}")

                # Prepare volume-specific data
                volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
                volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"

                # Generate TOC for this volume
                toc_result = await self.proposal_outline_service.generate_table_of_contents(
                    opportunity_id=str(item.opps_id),
                    tenant_id= "SYSTEM",
                    source="sam",
                    volume_information=volume_structure,
                    content_compliance=volume_content,
                    is_rfp=ProposalUtilities.is_rfp(str(opportunity_type))
                )

                # Extract TOC JSON
                toc_data = None
                if toc_result and "content" in toc_result:
                    content = toc_result["content"]
                    try:
                        # Look for JSON in the content
                        import re
                        json_match = re.search(r'\{.*\}', content, re.DOTALL)
                        if json_match:
                            json_str = json_match.group()
                            toc_data = json.loads(json_str)
                    except:
                        pass

                if toc_data and "table_of_contents" in toc_data:
                    volume_toc = toc_data["table_of_contents"]
                    logger.info(f"✓ TOC generated successfully with {len(volume_toc)} sections")
                else:
                    logger.error("⚠ TOC generation failed or no valid JSON found")
                    volume_toc = []

                update_fields[toc_fields[idx]] = json.dumps(volume_toc, ensure_ascii=False)

            if update_fields:
                async for db in get_kontratar_db():
                    updated_record = await OppsController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=item.opps_id,
                        update_fields=update_fields
                    )
                    break
                if updated_record:
                    logger.info(f"Successfully stored Table of Contents for SAM opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update OppsTable with TOC for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Table of Contents for SAM opportunity {item.opps_id}: {e}")
    
    
    async def _generate_structure_compliance(self, item):
        """Generate structure compliance and store in OppsTable"""
        logger.info(f"Generating structure compliance for SAM opportunity: {item.opps_id}")

        try:
            structure_result = await self.structure_compliance_service.generate_structure_compliance(
                opportunity_id=item.opps_id,
                tenant_id=getattr(item, "tenant_id", None) or "default",
                source=getattr(item, "opps_source", None) or "sam",
                max_tokens=2048
            )

            # Extract the generated content
            structure_content = structure_result.get("structured_data", {})
            structure_content_str = str(structure_content)

            update_fields = {
                "structure_compliance":  structure_content_str
            }
            async for db in get_kontratar_db():
                updated_record = await OppsController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=item.opps_id,
                    update_fields=update_fields
                )
                break  # Now the session will close
            if updated_record:
                logger.info(f"Successfully stored structure compliance for SAM opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update OppsTable for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating structure compliance for SAM opportunity {item.opps_id}: {e}")

    
    async def _generate_content_compliance(self, item):
        """Generate content compliance and store in OppsTable"""
        logger.info(f"Generating content compliance for SAM opportunity: {item.opps_id}")

        try:
            async for db in get_kontratar_db(): 
                is_rfp = await self._is_rfp_opportunity(db, item.opps_id)
                break  # Now the session will close
            content_result = await self.content_compliance_service.generate_content_compliance(
                opportunity_id=item.opps_id,
                tenant_id=getattr(item, "tenant_id", None) or "default",
                source=getattr(item, "opps_source", None) or "sam",
                is_rfp=is_rfp,
            )
            # Extract the generated content
            content_compliance_content = content_result.get("structured_data", {})
            
            if not content_compliance_content:
                logger.warning(f"No content compliance generated for opportunity: {item.opps_id}")
                return
            
            content_compliance_str = str(content_compliance_content)
            logger.info(f"Content compliance generated for opportunity: {item.opps_id} (Type: {'RFP' if is_rfp else 'RFI'})")

            update_fields = {
                "content_compliance": content_compliance_str
            }
            async for db in get_kontratar_db():
                updated_record = await OppsController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=item.opps_id,
                    update_fields=update_fields
                )
                break  # Now the session will close
            if updated_record:
                logger.info(f"Successfully stored content compliance for SAM opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update OppsTable for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating content compliance for SAM opportunity {item.opps_id}: {e}")    
    
    async def _generate_formatting_requirements(self, item):
        """Generate formatting requirements and store in OppsTable"""
        logger.info(f"Generating formatting requirements for SAM opportunity: {item.opps_id}")

        try:
            formatting_result = await self.formatting_requirements_service.generate_formatting_requirements(
                opportunity_id=item.opps_id,
                tenant_id=getattr(item, "tenant_id", None) or "default",
                source=getattr(item, "opps_source", None) or "sam",
                max_tokens=1024
            )
            formatting_content = formatting_result.get("content", "")
        

            # Extract actual content string if it's a response object
            if hasattr(formatting_content, 'content'):
                formatting_content_str = formatting_content.content
            else:
                formatting_content_str = str(formatting_content)

            format_content_json_dic = ProposalUtilities.extract_json_from_brackets(formatting_content_str)
            if  not format_content_json_dic:
                logger.warning(f"Could not extract valid JSON from formatting requirements for opportunity: {item.opps_id}")

            if not formatting_content:
                logger.warning(f"No formatting requirements generated for SAM opportunity: {item.opps_id}")
                return
           
            if isinstance(format_content_json_dic, dict) and "document_guidelines" in format_content_json_dic:
                # Extract just the list under "document_guidelines"
                format_content_list = format_content_json_dic["document_guidelines"]
            else:
                format_content_list = format_content_json_dic
            format_content_json_test = json.dumps(format_content_json_dic, ensure_ascii=False) # Convert to JSON string to test validity
            format_content_json_str = json.dumps(format_content_list, ensure_ascii=False)  # Convert to JSON string
            # Validate JSON
            try:
                parsed_json = json.loads(format_content_json_test)
                logger.info(f"Valid JSON formatting requirements generated for SAM opportunity: {item.opps_id}")
                # Optionally, validate structure
                if hasattr(self.formatting_requirements_service, 'is_valid_formatting_requirements'):
                    if "document_guidelines" in parsed_json:
                        is_valid = self.formatting_requirements_service.is_valid_formatting_requirements(
                            parsed_json["document_guidelines"]
                        )
                        if not is_valid:
                            logger.warning(f"Generated formatting requirements failed validation for SAM opportunity: {item.opps_id}")
            except Exception as e:
                logger.warning(f"Formatting requirements not valid JSON: {e}")

            update_fields = {
                "format_compliance": format_content_json_str
            }
            async for db in get_kontratar_db():
                updated_record = await OppsController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=item.opps_id,
                    update_fields=update_fields
                )
                break
            if updated_record:
                logger.info(f"Successfully stored formatting requirements for SAM opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update OppsTable for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating formatting requirements for SAM opportunity {item.opps_id}: {e}")

    
    async def _generate_technical_requirements(self, item):
        """Generate technical requirements and store in OppsTable"""
        logger.info(f"Generating technical requirements for SAM opportunity: {item.opps_id}")

        try:
            tech_result = await self.tech_requirements_service.generate_technical_requirements(
                opportunity_id=item.opps_id,
                tenant_id=getattr(item, "tenant_id", None) or "default",
                source=getattr(item, "opps_source", None) or "sam",
                max_tokens=2048
            )
            tech_requirements_content = tech_result.get("content", "")
            
          
            
                   # Extract actual content string if it's a response object
            if hasattr(tech_requirements_content, 'content'):
                tech_req_str = tech_requirements_content.content
            else:
                tech_req_str = str(tech_requirements_content)

                  # Extract only the JSON part
            tech_req_json_dic = ProposalUtilities.extract_json_from_brackets(tech_req_str)

            if not tech_req_json_dic:
                logger.warning(f"Could not extract valid JSON from tech requirements for opportunity: {item.opps_id}")

            if not tech_requirements_content:
                logger.warning(f"No technical requirements generated for SAM opportunity: {item.opps_id}")
                return
            # Extract just the list under "technicalRequirements"
            if isinstance(tech_req_json_dic, dict) and "technicalRequirements" in tech_req_json_dic:
                tech_req_list = tech_req_json_dic["technicalRequirements"]
            else:
                tech_req_list = tech_req_json_dic  # fallback, in case it's already a list
            
            tech_req_json_str = json.dumps(tech_req_list, ensure_ascii=False)        # Convert to JSON string
            # Validate JSON
            try:
                json.loads(tech_req_json_str)
                logger.info(f"Valid JSON technical requirements generated for SAM opportunity: {item.opps_id}")
            except Exception as e:
                logger.warning(f"Technical requirements not valid JSON: {e}")

            update_fields = {
                "requirement_text": tech_req_json_str
            }
            async for db in get_kontratar_db():
                updated_record = await OppsController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=item.opps_id,
                    update_fields=update_fields
                )
                break
            if updated_record:
                logger.info(f"Successfully stored technical requirements for SAM opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update OppsTable for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating technical requirements for SAM opportunity {item.opps_id}: {e}")

    async def _generate_key_personnel_titles(self, item: OppsTable):
        """Generate key personnel titles and store in KeyPersonnelUploads"""
        logger.info(f"Generating key personnel titles for opportunity: {item.opps_id}")
        
        try:
            # Generate key personnel titles using the service
            key_personnel = KeyPersonnelService()

            key_personnel_titles = await key_personnel.extract_key_personnel(
                opportunity_id=str(item.opps_id),
                tenant_id="SYSTEM",
                source="sam",  
            )

            update_fields = {
                    "key_personnel": json.dumps(key_personnel_titles, ensure_ascii=False)
            }

            async for db in get_kontratar_db():
                updated_record = await OppsController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=item.opps_id,
                    update_fields=update_fields
                )
                break
        
            if updated_record:
                logger.info(f"Successfully stored key personnel titles for opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update key personnel titles for opportunity: {item.opps_id}")
            
            
        except Exception as e:
            logger.error(f"Error generating key personnel titles for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue
            
    
    def enable(self):
        self.is_enabled = True
        logger.info("SAM opps scheduler enabled")
    
    def disable(self):
        self.is_enabled = False
        logger.info("SAM opps scheduler disabled")
    
    def is_scheduler_enabled(self) -> bool:
        return self.is_enabled
    
    def start(self, interval_seconds: int = 30):
        if self.is_running:
            logger.warning("SAM opps scheduler is already running")
            return
        self.scheduler.add_job(
            self.process_sam_opps_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_sam_opps_queue",
            name="Process SAM Opps Queue"
        )
        self.scheduler.start()
        self.is_running = True
        logger.info(f"SAM opps scheduler started with {interval_seconds} second interval")
    
    def stop(self):
        if not self.is_running:
            logger.warning("SAM opps scheduler is not running")
            return
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("SAM opps scheduler stopped")
    
    def restart(self, interval_seconds: int = 30):
        logger.info("Restarting SAM opps scheduler...")
        self.stop()
        self.start(interval_seconds)
    
    def get_status(self) -> Dict[str, Any]:
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs 
