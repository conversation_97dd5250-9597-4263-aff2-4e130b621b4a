import asyncio
import json
from typing import Any, Dict, List
from utils.semaphore import run_with_semaphore
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from services.proposal.key_personnel import KeyPersonnelService
from database import get_customer_db
from loguru import logger
from services.queue_service.custom_opps_queue_service import \
    CustomOppsQueueService
from sqlalchemy.ext.asyncio import AsyncSession
import ast


from models.customer_models import CustomOppsQueue as QueueItem, CustomOppsTable, DataMetastore
from models.customer_models import DataMetastore
from controllers.customer.proposal_outline_queue_controller import ProposalOutlineQueueController

from controllers.customer.datametastore_controller import DataMetastoreController
from controllers.customer.custom_opportunity_table_info_controller import CustomOpportunityTableInfoController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController

from services.proposal.technical_requirements import TechnicalRequirementsService
from services.data_load.process_document import DocumentProcessingService
from services.proposal.formatting_requirements import FormattingRequirementsService
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.outline import ProposalOutlineService
from services.proposal.utilities import ProposalUtilities
from services.proposal.summary import SummaryService
from services.proposal.search_queries import ExtractKeywords
from services.proposal.document_title_extraction_service import DocumentTitleExtractionService
from services.scheduler_service.schedule_lock_service import ScheduleLockService

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
class CustomOppsSchedulerService:
    """Scheduler service for processing custom opportunities queue items"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False  # Set to True to enable processing
        self.tech_requirements_service = TechnicalRequirementsService()
        self.formatting_requirements_service = FormattingRequirementsService()
        self.content_compliance_service = ContentComplianceService()
        self.structure_compliance_service = StructureComplianceService()
        self.proposal_outline_service = ProposalOutlineService()
        self.summary_service = SummaryService()
        self.keywords_service = ExtractKeywords()
        self.document_title_extraction_service = DocumentTitleExtractionService()
        self.lock_service = ScheduleLockService("CUSTOM_OPPS_SCHEDULER_LOCK", "CUSTOM_OPPS_SCHEDULER")
        
    async def process_custom_opps_queue(self):
        """Process new custom opps queue items"""
        if not self.is_enabled:
            logger.info("Custom opps scheduler is disabled, skipping processing")
            return
        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Custom opps scheduler lock not acquired, skipping this run")
            return

        CONCURRENCY_LIMIT = 2
        MAX_ITEMS_PER_RUN = 10
            
        try:
            async for db in get_customer_db():
                items = await CustomOppsQueueService.claim_new_queue_items(db, limit=MAX_ITEMS_PER_RUN)
                break
            if not items:
                logger.info("No new custom opps queue items found")
                return
            
            logger.info(f"Processing {len(items)} new custom opps queue items")

            # Process items with a semaphore so new jobs start as soon as a slot frees up
            async def _on_enter(item: QueueItem):
                await self.update_batch_status([str(item.opps_id)], "AI_PROCESSING")

            await run_with_semaphore(
                items=items,
                max_jobs=CONCURRENCY_LIMIT,
                on_enter=_on_enter,
                worker=self.process_item,
            )

        except Exception as e:
            logger.error(f"Error in process_custom_opps_queue: {e}")
        finally:
            await self.lock_service.release_lock()

    ## Method to update item status
    async def update_batch_status(self, opportunity_ids: List[str], status: str) -> None:
        logger.info(f"Updating status for batch of {len(opportunity_ids)} opportunity IDs to '{status}'")
        try:
            async for db in get_customer_db():
                result = await CustomOppsQueueService.update_status_batch(db, opportunity_ids, status)
                if result:
                    logger.info(f"Successfully updated status for {len(opportunity_ids)} opportunity IDs to '{status}'")
                else:
                    logger.warning(f"Failed to update status for {len(opportunity_ids)} opportunity IDs to '{status}'")
                break
        except Exception as e:
            logger.error(f"Exception occurred while updating batch status: {e}")
    
    ## Method to process item while updating status
    async def process_item(self, item: QueueItem):
        opportunity_id = str(item.opps_id)
        try:
            # Process the item (implement your AI processing logic here)
            await self._process_custom_opps_item(item)

            async for db in get_customer_db():
                # Update status to COMPLETED
                await CustomOppsQueueService.update_custom_opps_queue_status(
                    db, opportunity_id, "AI_COMPLETED"
                )
                break
            async for db in get_customer_db():
                await ProposalOutlineQueueController.add_to_queue_from_worker(
                    db=db,
                    opps_id=item.opps_id,
                    tenant_id="SYSTEM",  # Use SYSTEM tenant for scheduler
                    outline_type="custom",
                    first_request=True
                )
                break

            logger.info(f"Successfully processed custom opps queue item {item.opps_id}")

        except Exception as e:
            logger.error(f"Error processing custom opps queue item {item.opps_id}: {e}")
            # Update status to FAILED
            async for db in get_customer_db():
                await CustomOppsQueueService.update_custom_opps_queue_status(
                    db, opportunity_id, "AI_FAILED"
                )
                break

        except Exception as e:
            logger.error(f"Error in process_custom_opps_queue: {e}")
        finally:
            await self.lock_service.release_lock()
    
    async def _process_custom_opps_item(self,item: QueueItem):
        """Process a custom opps queue item"""
        logger.info(f"Processing custom opps item: {item.opps_id}")
        logger.info(f"Opps source: {item.opps_source}")
        logger.info(f"Tenant ID: {item.tenant_id}")

        ## Get uploaded opportunity documents from database
        async for db in get_customer_db():
            documents: List[DataMetastore] = await DataMetastoreController.get_opportunity_documents(db, str(item.opps_id))
            break
        #document = await CustomOpportunityTableInfoController.get_opportunity_document(item.opps_id, item.tenant_id, db)
        #document_text = document.opps_raw_text

        texts = []
        result = []

        ## Get each document and process the text
        for document in documents:
            file_name = str(document.original_document_file_name)
            if file_name == f"{item.opps_id}.txt":
                continue
            raw_text = document.raw_text_document
            if isinstance(raw_text, bytes):
                plain_text = raw_text.decode("utf-8")
            else:
                plain_text = raw_text
            texts.append(DocumentProcessingService.process_corpus(str(plain_text)))
        
        logger.info(f"Extracted {len(texts)} documents for opportunity {item.opps_id}")
        logger.debug(f"Documents text only first 100 chars: {[text[:100] for text in texts]}")
        ## For each processed text, use sematic chunking to split it
        for text in texts:
            result.extend(DocumentProcessingService.semantic_chunk(text))

        ## Them insert the documents to the vector database
        collection_name = f"{item.tenant_id}_{item.opps_id}"
        await DocumentProcessingService.add_documents(collection_name, result)

        ## Summary and other prelimniary AI generation will be done here        
        # Define the operations in order
        # Generate a summary for the opportunity
        await self._generate_summary(item)

        # Generate technical requirements from the documents
        await self._generate_technical_requirements(item)

        # Generate formatting requirements for the opportunity
        await self._generate_formatting_requirements(item)

        # Generate content compliance information
        await self._generate_content_compliance(item)

        # Generate structure compliance information
        await self._generate_structure_compliance(item)

        # Generate a table of contents for the opportunity documents
        await self._generate_table_of_contents(item)

        # # Generate a proposal outline based on the opportunity
        # await self._generate_proposal_outline(item)

        # Generate and save keyword search queries for the opportunity
        await self._generate_keywords(item, texts)

        # Generate key personnel titles required for the opportunity
        await self._generate_key_personnel_titles(item)

        # Generate submission requirements and email details
        await self._generate_submission_requirements(item)

        logger.info(f"Completed processing custom opps item: {item.opps_id}")
        
    async def _is_rfp_opportunity(self, db: AsyncSession, opportunity_id: str) -> bool:
        """
        Check if the opportunity is an RFP based on opportunity_type in CustomOppsTable
        Returns True if it's an RFP, False if it's an RFI
        """
        try:
            from sqlalchemy import select
            from models.customer_models import CustomOppsTable
            
            # Query the CustomOppsTable to get opportunity_type
            query = select(CustomOppsTable.opportunity_type).where(
                CustomOppsTable.opportunity_id == opportunity_id
            )
            result = await db.execute(query)
            opportunity_type = result.scalar_one_or_none()
            
            if not opportunity_type:
                logger.warning(f"No opportunity_type found for opportunity_id: {opportunity_id}, defaulting to RFI")
                return False
            
            # Convert to lowercase for case-insensitive comparison
            opp_type_lower = opportunity_type.lower()
            
            # Check if it's an RFP based on the specified criteria
            rfp_types = [
                "combined synopsis",
                "solicitation", 
                "combined synopsis/solicitation"
            ]
            
            is_rfp_by_type = any(rfp_type in opp_type_lower for rfp_type in rfp_types)
            is_rfp_by_name = "request for proposal" in opp_type_lower or "rfp" in opp_type_lower
            
            is_rfp = is_rfp_by_type or is_rfp_by_name
            
            logger.info(f"Opportunity {opportunity_id} type: '{opportunity_type}' - Classified as: {'RFP' if is_rfp else 'RFI'}")
            return is_rfp
            
        except Exception as e:
            logger.error(f"Error checking RFP status for opportunity {opportunity_id}: {e}")
            # Default to RFI if there's an error
            return False
        
        
    async def _generate_keywords(self,item: QueueItem, texts: list):
        """Generate and save keyword search queries for the opportunity"""
        logger.info(f"Generating keyword search queries for opportunity: {item.opps_id}")
        try:
            async for db in get_customer_db():
                
                # Combine all plain texts into one string for LLM context
                plain_text = "\n\n".join(texts)
                queries = await self.keywords_service.get_queries(plain_text, get_related_queries=True)
                if not queries:
                    logger.warning(f"No keywords generated for opportunity: {item.opps_id}")
                    return

                # Save as JSON string in the keywords field
                keywords_json = json.dumps(queries, ensure_ascii=False)
                await CustomOpportunitiesController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=str(item.opps_id),
                    update_fields={"keywords": keywords_json}
                )
                logger.info(f"Successfully stored keywords for opportunity: {item.opps_id}")
        except Exception as e:
            logger.error(f"Error generating keywords for opportunity {item.opps_id}: {e}")
  
    
    
    async def _generate_summary(self,  item):
        """Generate and save summary for the opportunity"""
        logger.info(f"Generating summary for opportunity: {item.opps_id}")
        try:
            async for db in get_customer_db():
                # Generate summary using the SummaryService
                summary = await self.summary_service.generate_summary(
                    db=db,
                    tenant_id=item.tenant_id,
                    opportunity_id=item.opps_id,
                    source=item.opps_source or "custom"
                )
                break
            if not summary:
                logger.warning(f"No summary generated for opportunity: {item.opps_id}")
                return

            # Save the summary using the controller
            async for db in get_customer_db():
                await self.summary_service.set_summary(
                    db=db,
                    tenant_id=item.tenant_id,
                    opportunity_id=item.opps_id,
                    source=item.opps_source or "custom",
                    summary=summary
                )
                break
            logger.info(f"Successfully stored summary for opportunity: {item.opps_id}")
        except Exception as e:
            logger.error(f"Error generating summary for opportunity {item.opps_id}: {e}")

    
    async def _generate_proposal_outline(self, item: QueueItem):
        """Generate proposal outline for each TOC and store in CustomOppsTable"""
        logger.info(f"Generating Proposal Outline for opportunity: {item.opps_id}")

        try:
            opportunity_id = str(item.opps_id)
            # Retrieve all TOCs from DB
            async for db in get_customer_db():
                record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
                break
            if not record:
                row = None
            else:
                row = (
                    record.toc_text,
                    record.toc_text_2,
                    record.toc_text_3,
                    record.toc_text_4,
                    record.toc_text_5,
                )
            if not row:
                logger.warning(f"No TOC found for opportunity: {item.opps_id}")
                return

            toc_fields = ["toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"]
            outline_fields = [
                "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
                "proposal_outline_4", "proposal_outline_5"
            ]
            update_fields = {}

            for idx, toc_text in enumerate(row):
                if toc_text is None:
                    continue
                try:
                    toc_json = json.loads(str(toc_text))
                except Exception:
                    logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for opportunity: {item.opps_id}")
                    continue

                table_of_contents = toc_json
                if not table_of_contents:
                    logger.warning(f"No table_of_contents in {toc_fields[idx]} for opportunity: {item.opps_id}")
                    continue

                # Generate the outline using the ProposalOutlineService
                outline_result = await self.proposal_outline_service.generate_outline(
                    opportunity_id=str(item.opps_id),
                    tenant_id=str(item.tenant_id),
                    source=str(item.opps_source) or "custom",
                    table_of_contents=table_of_contents
                )
                outline_content = outline_result.get("outlines", "")
                
                
                logger.debug(f"Outline content for {toc_fields[idx]}: {str(outline_content)[:100]}...")
                if not outline_content:
                    logger.warning(f"No outline generated for TOC field {toc_fields[idx]} in SAM opportunity: {item.opps_id}")
                    continue  # Don't return, just skip this one
                
                # Store the outline JSON string in the appropriate field
                outline_content_json_str = json.dumps(outline_content, ensure_ascii=False)
                update_fields[outline_fields[idx]] = outline_content_json_str


            if update_fields:
                async for db in get_customer_db():
                    updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=opportunity_id,
                        update_fields=update_fields
                    )
                    break
                if updated_record:
                    logger.info(f"Successfully stored Proposal Outline for opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update CustomOppsTable with Proposal Outline for opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Proposal Outline for opportunity {item.opps_id}: {e}")

    
    async def _generate_table_of_contents(self, item: QueueItem):
        """Generate Table of Contents for each volume and store in CustomOppsTable"""
        logger.info(f"Generating Table of Contents for opportunity: {item.opps_id}")

        try:
            # Retrieve structure compliance and content compliance from DB
            query = select(CustomOppsTable.structure_compliance, CustomOppsTable.content_compliance, CustomOppsTable.opportunity_type).where(
                CustomOppsTable.opportunity_id == item.opps_id
            )
            async for db in get_customer_db():
                result = await db.execute(query)
                break
            row = result.first()
            if not row or not row[0]:
                logger.warning(f"No structure compliance found for opportunity: {item.opps_id}")
                return

            structure_compliance_str, content_compliance_str, opportunity_type = row
            # Parse structure compliance JSON
            structure_compliance = ast.literal_eval(structure_compliance_str)
            content_compliance = ast.literal_eval(content_compliance_str)
            if not structure_compliance or "structure" not in structure_compliance:
                logger.warning(f"Invalid structure compliance JSON for opportunity: {item.opps_id}")
                return
            
            volume_definitions = structure_compliance["structure"]

            # Split compliance data by volume
            volumes = {}

            # Extract structure data
            for volume in volume_definitions:
                volume_title = volume.get("volume_title", "")
                volumes[volume_title] = {
                    "structure": volume,
                    "content": None
                }

            # Match content compliance to volumes
            if content_compliance and "content_compliance" in content_compliance:
                logger.info("Using structured content compliance data...")
                for compliance in content_compliance["content_compliance"]:
                    volume_title = compliance.get("volume_title", "")
                    # Match volume titles
                    for vol_key in volumes.keys():
                        if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                            volumes[vol_key]["content"] = compliance
                            break

            else:
                logger.warning("No structured content compliance, will use full content for each volume")
                # Use full content compliance for each volume
                for vol_key in volumes.keys():
                    volumes[vol_key]["content"] = {"content": content_compliance}

            logger.info(f"✓ Split compliance into {len(volumes)} volumes: {list(volumes.keys())}")

            update_fields = {}
            toc_fields = [
                "toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"
            ]

            # Process each volume through the complete pipeline
            for idx, (volume_title, volume_data) in enumerate(volumes.items()):
                if idx >= len(toc_fields):
                    logger.warning(f"Skipping volume '{volume_title}' because there are more than {len(toc_fields)} volumes.")
                    continue

                logger.info(f"PROCESSING VOLUME: {volume_title}")
                
                
                # Prepare volume-specific data
                volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
                volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"

                # Generate TOC for this volume
                toc_result = await self.proposal_outline_service.generate_table_of_contents(
                    opportunity_id=str(item.opps_id),
                    tenant_id=str(item.tenant_id),
                    source=str(item.opps_source) or "custom",
                    volume_information=volume_structure,
                    content_compliance=volume_content,
                    is_rfp=ProposalUtilities.is_rfp(str(opportunity_type))
                )

                # Extract TOC JSON
                toc_data = None
                if toc_result and "content" in toc_result:
                    content = toc_result["content"]
                    try:
                        # Look for JSON in the content
                        import re
                        json_match = re.search(r'\{.*\}', content, re.DOTALL)
                        if json_match:
                            json_str = json_match.group()
                            toc_data = json.loads(json_str)
                    except:
                        pass

                if toc_data and "table_of_contents" in toc_data:
                    volume_toc = toc_data["table_of_contents"]
                    logger.info(f"✓ TOC generated successfully with {len(volume_toc)} sections")
                else:
                    logger.error("⚠ TOC generation failed or no valid JSON found")
                    volume_toc = []

                update_fields[toc_fields[idx]] = json.dumps(volume_toc, ensure_ascii=False)


            if update_fields:
                async for db in get_customer_db():
                    updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=str(item.opps_id),
                        update_fields=update_fields
                    )
                    break
                if updated_record:
                    logger.info(f"Successfully stored Table of Contents for opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update CustomOppsTable with TOC for opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Table of Contents for opportunity {item.opps_id}: {e}")

    
    async def _generate_structure_compliance(self,  item: QueueItem):
        """Generate structure compliance and store in CustomOppsTable"""
        logger.info(f"Generating structure compliance for opportunity: {item.opps_id}")
        
        try:
            # Generate structure compliance using the service
            structure_result = await self.structure_compliance_service.generate_structure_compliance(
                opportunity_id=str(item.opps_id),
                tenant_id=str(item.tenant_id),
                source=str(item.opps_source) or "custom",  # Default to "custom" if source is None
                max_tokens=2048
            )
            
            # Extract the generated content
            structure_content = structure_result.get("structured_data", {})
            structure_content_str = str(structure_content)
            
            # Update the CustomOppsTable with the structure compliance
            update_fields = {
                "structure_compliance": structure_content_str
            }
            async for db in get_customer_db():
                updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=str(item.opps_id),
                    update_fields=update_fields
                )
                break
            
            if updated_record:
                logger.info(f"Successfully stored structure compliance for opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update CustomOppsTable for opportunity: {item.opps_id}")
                
        except Exception as e:
            logger.error(f"Error generating structure compliance for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue
       
        
    async def _generate_content_compliance(self, item: QueueItem):
        """Generate content compliance and store in CustomOppsTable"""
        logger.info(f"Generating content compliance for opportunity: {item.opps_id}")
        
        try:
            # First, determine if this is an RFP or RFI
            async for db in get_customer_db():
                is_rfp = await self._is_rfp_opportunity(db, str(item.opps_id))
                break
            # Generate content compliance using the service
            content_result = await self.content_compliance_service.generate_content_compliance(
                opportunity_id=str(item.opps_id),
                tenant_id=str(item.tenant_id),
                source=str(item.opps_source) or "custom",  # Default to "custom" if source is None
                is_rfp=is_rfp,
            )
            
            # Extract the generated content
            content_compliance_content = content_result.get("structured_data", {})
            
            if not content_compliance_content:
                logger.warning(f"No content compliance generated for opportunity: {item.opps_id}")
                return
            
            content_compliance_str = str(content_compliance_content)
            logger.info(f"Content compliance generated for opportunity: {item.opps_id} (Type: {'RFP' if is_rfp else 'RFI'})")
            
            # Update the CustomOppsTable with the content compliance
            update_fields = {
                "content_compliance": content_compliance_str
            }
            async for db in get_customer_db():
                updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=str(item.opps_id),
                    update_fields=update_fields
                )
                break
            
            if updated_record:
                logger.info(f"Successfully stored content compliance for opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update CustomOppsTable for opportunity: {item.opps_id}")
                
        except Exception as e:
            logger.error(f"Error generating content compliance for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue
    
    async def _generate_technical_requirements(self, item: QueueItem):
    
            """Generate technical requirements and store in CustomOppsTable"""
            logger.info(f"Generating technical requirements for opportunity: {item.opps_id}")
        
            try:
                 # Generate technical requirements using the service
                tech_result = await self.tech_requirements_service.generate_technical_requirements(
                    opportunity_id=str(item.opps_id),
                    tenant_id=str(item.tenant_id),
                    source=str(item.opps_source) or "custom",  # Default to "custom" if source is None
                    max_tokens=2048
                )
            
            # Extract the generated content
                tech_requirements_content = tech_result.get("content", "")
            
                     # Extract actual content string if it's a response object
                if hasattr(tech_requirements_content, 'content'):
                    tech_req_str = tech_requirements_content.content
                else:
                    tech_req_str = str(tech_requirements_content)

                    # Extract only the JSON part
                tech_req_json_dic = ProposalUtilities.extract_json_from_brackets(tech_req_str)

                if not tech_req_json_dic:
                    logger.warning(f"Could not extract valid JSON from tech requirements for opportunity: {item.opps_id}")

                if not tech_requirements_content:
                    logger.warning(f"No technical requirements generated for SAM opportunity: {item.opps_id}")
                    return
                # Extract just the list under "technicalRequirements"
                if isinstance(tech_req_json_dic, dict) and "technicalRequirements" in tech_req_json_dic:
                    tech_req_list = tech_req_json_dic["technicalRequirements"]
                else:
                    tech_req_list = tech_req_json_dic  # fallback, in case it's already a list
                
                tech_req_json_str = json.dumps(tech_req_list, ensure_ascii=False)        # Convert to JSON string
            # Validate that it's valid JSON (since the service returns JSON)
                try:
                # Test if it's valid JSON
                    json.loads(tech_req_json_str)
                    logger.info(f"Valid JSON technical requirements generated for opportunity: {item.opps_id}")
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON generated for technical requirements: {e}")
                # Store as text anyway, but log the issue
                    pass
            
            # Update the CustomOppsTable with the technical requirements
                update_fields = {
                    "requirement_text": tech_req_json_str
                }
                async for db in get_customer_db():
                    updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=str(item.opps_id),
                        update_fields=update_fields
                    )
                    break
            
                if updated_record:
                    logger.info(f"Successfully stored technical requirements for opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update CustomOppsTable for opportunity: {item.opps_id}")
                
            except Exception as e:
                logger.error(f"Error generating technical requirements for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue
            
    async def _generate_formatting_requirements(self, item: QueueItem):
        """Generate formatting requirements and store in CustomOppsTable"""
        logger.info(f"Generating formatting requirements for opportunity: {item.opps_id}")
        
        try:
            # Generate formatting requirements using the service
            formatting_result = await self.formatting_requirements_service.generate_formatting_requirements(
                opportunity_id=str(item.opps_id),
                tenant_id=str(item.tenant_id),
                source=str(item.opps_source) or "custom",  # Default to "custom" if source is None
                max_tokens=1024
            )
            
            # Extract the generated content
            formatting_content = formatting_result.get("content", "")
            
             # Extract actual content string if it's a response object
            if hasattr(formatting_content, 'content'):
                formatting_content_str = formatting_content.content
            else:
                formatting_content_str = str(formatting_content)

            format_content_json_dic = ProposalUtilities.extract_json_from_brackets(formatting_content_str)
            if  not format_content_json_dic:
                logger.warning(f"Could not extract valid JSON from formatting requirements for opportunity: {item.opps_id}")

            if not formatting_content:
                logger.warning(f"No formatting requirements generated for SAM opportunity: {item.opps_id}")
                return
           
            if isinstance(format_content_json_dic, dict) and "document_guidelines" in format_content_json_dic:
                # Extract just the list under "document_guidelines"
                format_content_list = format_content_json_dic["document_guidelines"]
            else:
                format_content_list = format_content_json_dic
            format_content_json_test = json.dumps(format_content_json_dic, ensure_ascii=False) # Convert to JSON string to test validity
            format_content_json_str = json.dumps(format_content_list, ensure_ascii=False)  # Convert to JSON string
            
            # Validate that it's valid JSON (since the service returns JSON)
            try:
                # Test if it's valid JSON
                parsed_json = json.loads(format_content_json_test)
                logger.info(f"Valid JSON formatting requirements generated for opportunity: {item.opps_id}")
                
                # Additional validation using the service's validation method
                if hasattr(self.formatting_requirements_service, 'is_valid_formatting_requirements'):
                    if "document_guidelines" in parsed_json:
                        is_valid = self.formatting_requirements_service.is_valid_formatting_requirements(
                            parsed_json["document_guidelines"]
                        )
                        if not is_valid:
                            logger.warning(f"Generated formatting requirements failed validation for opportunity: {item.opps_id}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON generated for formatting requirements: {e}")
                # Store as text anyway, but log the issue
                pass
            
            # Update the CustomOppsTable with the formatting requirements
            update_fields = {
                "format_compliance": format_content_json_str
            }
            async for db in get_customer_db():
                updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=str(item.opps_id),
                    update_fields=update_fields
                )
                break
            
            if updated_record:
                logger.info(f"Successfully stored formatting requirements for opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update CustomOppsTable for opportunity: {item.opps_id}")
                
        except Exception as e:
            logger.error(f"Error generating formatting requirements for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue
    
    async def _generate_key_personnel_titles(self, item: QueueItem):
        """Generate key personnel titles and store in KeyPersonnelUploads"""
        logger.info(f"Generating key personnel titles for opportunity: {item.opps_id}")
        
        try:
            # Generate key personnel titles using the service
            key_personnel = KeyPersonnelService()

            key_personnel_titles = await key_personnel.extract_key_personnel(
                opportunity_id=str(item.opps_id),
                tenant_id=str(item.tenant_id),
                source=str(item.opps_source) or "custom",  # Default to "custom" if source is None
            )

            update_fields = {
                    "key_personnel": json.dumps(key_personnel_titles, ensure_ascii=False)
            }

            async for db in get_customer_db():
                updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=str(item.opps_id),
                    update_fields=update_fields
                )
                break
        
            if updated_record:
                logger.info(f"Successfully stored key personnel titles for opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update key personnel titles for opportunity: {item.opps_id}")
            
            
        except Exception as e:
            logger.error(f"Error generating key personnel titles for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue

    async def _generate_submission_requirements(self, item: QueueItem):
        """Generate submission requirements and store in CustomOppsTable"""
        logger.info(f"Generating submission requirements for opportunity: {item.opps_id}")

        try:
            # Get opportunity data from database
            async for db in get_customer_db():
                opportunity_record = await CustomOpportunitiesController.get_by_opportunity_id(
                    db, str(item.opps_id)
                )
                break

            if not opportunity_record:
                logger.warning(f"No opportunity record found for opportunity: {item.opps_id}")
                return

            # Prepare opportunity data for the extraction service
            opportunity_data = {
                "title": opportunity_record.title,
                "description": opportunity_record.description,
                "description_text": opportunity_record.description_text,
                "solicitation_number": opportunity_record.opportunity_id,
                "opportunity_type": opportunity_record.opportunity_type,
                "point_of_contact_email": opportunity_record.point_of_contact_email,
                "point_of_contact_first_name": opportunity_record.point_of_contact_first_name,
                "point_of_contact_last_name": opportunity_record.point_of_contact_last_name,
                "point_of_contact_phone": opportunity_record.point_of_contact_phone,
                "expiration_date": opportunity_record.expiration_date,
                "posted_date": opportunity_record.posted_date
            }

            # Extract submission requirements using the service
            submission_requirements = await self.document_title_extraction_service.extract_submission_requirements(
                opportunity_data=opportunity_data,
                source=str(item.opps_source) or "custom"
            )

            if not submission_requirements:
                logger.warning(f"No submission requirements generated for opportunity: {item.opps_id}")
                return

            # Convert to JSON string for storage
            submission_requirements_json = json.dumps(submission_requirements, ensure_ascii=False)

            # Update the CustomOppsTable with the submission requirements
            update_fields = {
                "submission_requirements": submission_requirements_json
            }

            async for db in get_customer_db():
                updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                    db=db,
                    opportunity_id=str(item.opps_id),
                    update_fields=update_fields
                )
                break

            if updated_record:
                logger.info(f"Successfully stored submission requirements for opportunity: {item.opps_id}")
            else:
                logger.error(f"Failed to update CustomOppsTable with submission requirements for opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating submission requirements for opportunity {item.opps_id}: {e}")
            # Don't re-raise the exception to allow other processing steps to continue


    def enable(self):
        """Enable the custom opps scheduler (allows jobs to run)"""
        self.is_enabled = True
        logger.info("Custom opps scheduler enabled")
    
    def disable(self):
        """Disable the custom opps scheduler (prevents jobs from running)"""
        self.is_enabled = False
        logger.info("Custom opps scheduler disabled")
    
    def is_scheduler_enabled(self) -> bool:
        """Check if custom opps scheduler is enabled"""
        return self.is_enabled
    
    def start(self, interval_seconds: int = 30):
        """Start the custom opps scheduler"""
        if self.is_running:
            logger.warning("Custom opps scheduler is already running")
            return
        
        # Add job to the scheduler
        self.scheduler.add_job(
            self.process_custom_opps_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_custom_opps_queue",
            name="Process Custom Opps Queue"
        )
        
        # Start the scheduler
        self.scheduler.start()
        self.is_running = True
        
        logger.info(f"Custom opps scheduler started with {interval_seconds} second interval")
    
    def stop(self):
        """Stop the custom opps scheduler"""
        if not self.is_running:
            logger.warning("Custom opps scheduler is not running")
            return
        
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Custom opps scheduler stopped")
    
    def restart(self, interval_seconds: int = 30):
        """Restart the custom opps scheduler"""
        logger.info("Restarting custom opps scheduler...")
        self.stop()
        self.start(interval_seconds)
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive custom opps scheduler status"""
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        """Get information about scheduled jobs"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs 