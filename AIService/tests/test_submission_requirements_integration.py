"""
Test script to verify submission requirements integration with schedulers
"""
import async<PERSON>
import json
from unittest.mock import AsyncMock, MagicMock

from services.proposal.document_title_extraction_service import DocumentTitleExtractionService
from services.scheduler_service.custom_opps_scheduler_service import CustomOppsSchedulerService
from services.scheduler_service.sam_opps_scheduler_service import SamOppsSchedulerService


def test_document_title_extraction_service_has_chromadb():
    """Test that the document title extraction service has ChromaDB integration"""
    service = DocumentTitleExtractionService()
    
    # Verify ChromaDB service is initialized
    assert hasattr(service, 'chroma_service')
    assert service.chroma_service is not None
    print("✓ DocumentTitleExtractionService has ChromaDB integration")


def test_extract_submission_requirements_signature():
    """Test that extract_submission_requirements has the correct signature"""
    service = DocumentTitleExtractionService()
    
    # Check method exists and has correct signature
    assert hasattr(service, 'extract_submission_requirements')
    method = getattr(service, 'extract_submission_requirements')
    assert callable(method)
    
    # Check method signature (this is a basic check)
    import inspect
    sig = inspect.signature(method)
    params = list(sig.parameters.keys())
    
    # Should have self, opportunity_id, tenant_id, source
    expected_params = ['opportunity_id', 'tenant_id', 'source']
    for param in expected_params:
        assert param in params, f"Missing parameter: {param}"
    
    print("✓ extract_submission_requirements has correct signature")


def test_custom_scheduler_has_submission_requirements_method():
    """Test that custom scheduler has the submission requirements generation method"""
    scheduler = CustomOppsSchedulerService()
    
    # Verify the method exists
    assert hasattr(scheduler, '_generate_submission_requirements')
    assert callable(getattr(scheduler, '_generate_submission_requirements'))
    
    # Verify the service is initialized
    assert hasattr(scheduler, 'document_title_extraction_service')
    assert isinstance(scheduler.document_title_extraction_service, DocumentTitleExtractionService)
    
    print("✓ Custom scheduler has submission requirements integration")


def test_sam_scheduler_has_submission_requirements_method():
    """Test that SAM scheduler has the submission requirements generation method"""
    scheduler = SamOppsSchedulerService()
    
    # Verify the method exists
    assert hasattr(scheduler, '_generate_submission_requirements')
    assert callable(getattr(scheduler, '_generate_submission_requirements'))
    
    # Verify the service is initialized
    assert hasattr(scheduler, 'document_title_extraction_service')
    assert isinstance(scheduler.document_title_extraction_service, DocumentTitleExtractionService)
    
    print("✓ SAM scheduler has submission requirements integration")


def test_database_models_have_submission_requirements_field():
    """Test that database models include the submission_requirements field"""
    from models.customer_models import CustomOppsTable
    from models.kontratar_models import OppsTable
    
    # Check CustomOppsTable has the field
    assert hasattr(CustomOppsTable, 'submission_requirements')
    
    # Check OppsTable has the field
    assert hasattr(OppsTable, 'submission_requirements')
    
    print("✓ Database models have submission_requirements field")


async def test_submission_requirements_json_structure():
    """Test that submission requirements follow expected JSON structure"""
    expected_structure = {
        "format_requirements": None,
        "naming_convention": None,
        "page_limits": None,
        "document_titles": [],
        "submission_deadlines": None,
        "email_submission": {
            "required": False,
            "email_address": None,
            "subject_format": None,
            "body_template": None,
            "cc_addresses": [],
            "bcc_addresses": [],
            "special_email_instructions": None
        },
        "alternative_submission_methods": [],
        "special_instructions": None
    }
    
    # Test serialization
    json_str = json.dumps(expected_structure, ensure_ascii=False)
    assert isinstance(json_str, str)
    
    # Test deserialization
    deserialized = json.loads(json_str)
    assert deserialized == expected_structure
    
    # Verify email submission structure
    email_info = deserialized["email_submission"]
    assert "required" in email_info
    assert "email_address" in email_info
    assert "subject_format" in email_info
    assert "body_template" in email_info
    
    print("✓ Submission requirements JSON structure is valid")


if __name__ == "__main__":
    # Run basic tests
    async def run_basic_tests():
        print("Testing submission requirements integration...")
        
        test_document_title_extraction_service_has_chromadb()
        test_extract_submission_requirements_signature()
        test_custom_scheduler_has_submission_requirements_method()
        test_sam_scheduler_has_submission_requirements_method()
        test_database_models_have_submission_requirements_field()
        await test_submission_requirements_json_structure()
        
        print("\nAll basic integration tests passed! ✅")
        print("\nIntegration Summary:")
        print("- DocumentTitleExtractionService updated to use ChromaDB")
        print("- extract_submission_requirements method signature updated")
        print("- Custom opportunities scheduler integrated")
        print("- SAM opportunities scheduler integrated")
        print("- Database models updated with submission_requirements field")
        print("- ChromaDB-based document retrieval implemented")

    asyncio.run(run_basic_tests())
