# Submission Requirements Integration

This document describes the integration of the `extract_submission_requirements` function from the `DocumentTitleExtractionService` into the scheduler services for both custom and SAM opportunities.

## Overview

The submission requirements extraction functionality has been integrated into both scheduler services to automatically extract and store submission requirements and email details from opportunity documents during the AI processing pipeline using ChromaDB for document retrieval.

## Integration Points

### 1. Custom Opportunities Scheduler (`custom_opps_scheduler_service.py`)

**Location**: `AIService/services/scheduler_service/custom_opps_scheduler_service.py`

**Changes Made**:
- Added import for `DocumentTitleExtractionService`
- Initialized the service in the constructor
- Added `_generate_submission_requirements()` method
- Integrated the method into the processing pipeline after key personnel generation

**Processing Flow**:
```
Document Processing → Summary → Technical Requirements → Formatting Requirements → 
Content Compliance → Structure Compliance → Table of Contents → Keywords → 
Key Personnel → **Submission Requirements** → Complete
```

### 2. SAM Opportunities Scheduler (`sam_opps_scheduler_service.py`)

**Location**: `AIService/services/scheduler_service/sam_opps_scheduler_service.py`

**Changes Made**:
- Added import for `DocumentTitleExtractionService`
- Initialized the service in the constructor
- Added `_generate_submission_requirements()` method
- Integrated the method into the processing pipeline after key personnel generation

**Processing Flow**:
```
Document Processing → Summary → Technical Requirements → Formatting Requirements → 
Content Compliance → Structure Compliance → Table of Contents → Keywords → 
Key Personnel → **Submission Requirements** → Complete
```

## Database Schema Changes

### New Fields Added

**CustomOppsTable** (`opportunity.custom_oppstable`):
- `submission_requirements` (TEXT) - JSON containing submission requirements and email details

**OppsTable** (`kontratar_main.oppstable`):
- `submission_requirements` (TEXT) - JSON containing submission requirements and email details

### Migration Script

Location: `AIService/migrations/add_submission_requirements_field.sql`

To apply the migration:
```sql
-- Add submission_requirements field to CustomOppsTable (customer opportunities)
ALTER TABLE opportunity.custom_oppstable 
ADD COLUMN IF NOT EXISTS submission_requirements TEXT;

-- Add submission_requirements field to OppsTable (SAM opportunities)  
ALTER TABLE kontratar_main.oppstable 
ADD COLUMN IF NOT EXISTS submission_requirements TEXT;
```

## JSON Structure

The `submission_requirements` field stores JSON data with the following structure:

```json
{
    "format_requirements": "PDF format required",
    "naming_convention": "Proposal_CompanyName_SolicitationNumber",
    "page_limits": "Technical proposal: 25 pages, Price proposal: 10 pages",
    "document_titles": ["Technical Proposal", "Price Proposal", "Past Performance"],
    "submission_deadlines": "2024-12-31 5:00 PM EST",
    "email_submission": {
        "required": true,
        "email_address": "<EMAIL>",
        "subject_format": "Proposal Submission - SAM-2024-001",
        "body_template": "Dear Contracting Officer,\n\nPlease find attached our proposal submission...",
        "cc_addresses": [],
        "bcc_addresses": [],
        "special_email_instructions": "Include all required documents as separate attachments"
    },
    "alternative_submission_methods": ["Electronic submission via portal"],
    "special_instructions": "All documents must be digitally signed"
}
```

## Implementation Details

### Custom Opportunities Processing

The `_generate_submission_requirements()` method in the custom scheduler:

1. Calls `DocumentTitleExtractionService.extract_submission_requirements()` with opportunity_id, tenant_id, and source
2. The service uses ChromaDB to retrieve relevant document chunks about submission requirements
3. LLM analyzes the context to extract structured submission requirements
4. Stores the JSON result in the `submission_requirements` field

### SAM Opportunities Processing

The `_generate_submission_requirements()` method in the SAM scheduler:

1. Calls `DocumentTitleExtractionService.extract_submission_requirements()` with opportunity_id, tenant_id="SYSTEM", and source="sam"
2. The service uses ChromaDB to retrieve relevant document chunks about submission requirements
3. LLM analyzes the context to extract structured submission requirements
4. Stores the JSON result in the `submission_requirements` field

### ChromaDB Integration

The `extract_submission_requirements()` method now uses ChromaDB for document retrieval:

1. **ChromaDB Query**: Uses a specialized query to find submission-related content:
   ```
   Find document submission requirements including:
   - Document format requirements (PDF, DOCX, etc.)
   - File naming conventions and requirements
   - Page limits or formatting requirements
   - Specific document titles required
   - Submission deadlines and due dates
   - Email submission details and addresses
   - Alternative submission methods
   - Special instructions for proposal format
   - Contact information for submissions
   - Required email subject lines or templates
   ```

2. **Collection Selection**: 
   - Custom opportunities: `{tenant_id}_{opportunity_id}`
   - SAM opportunities: `{opportunity_id}`

3. **Context Processing**: Retrieves up to 8 relevant chunks and cleans them for LLM analysis

4. **Database Selection**: Uses appropriate database based on source (customer_db for custom, kontratar_db for SAM)

### Error Handling

Both implementations include comprehensive error handling:
- Logs warnings if no context is found in ChromaDB
- Logs warnings if no submission requirements are generated
- Logs errors for any exceptions during processing
- Does not re-raise exceptions to allow other processing steps to continue
- Returns default requirements structure if extraction fails

## Method Signature

The updated method signature for `extract_submission_requirements`:

```python
async def extract_submission_requirements(
    self,
    opportunity_id: str,
    tenant_id: str,
    source: str = "custom"
) -> Dict[str, Any]:
```

**Parameters**:
- `opportunity_id`: The opportunity ID for ChromaDB collection lookup
- `tenant_id`: The tenant ID for ChromaDB collection naming (custom opportunities only)
- `source`: Source type ("custom", "sam", "ebuy") to determine database and collection naming

**Returns**:
- Dictionary containing structured submission requirements and email details

## Benefits

1. **ChromaDB Integration**: Uses the same document retrieval pattern as other services
2. **Better Context**: Retrieves relevant document chunks specifically about submission requirements
3. **Consistent Architecture**: Follows the same pattern as content_compliance, structure_compliance, etc.
4. **Improved Accuracy**: LLM has access to actual document content rather than limited metadata
5. **Scalable**: Can handle large documents through ChromaDB's chunking and retrieval
6. **Error Resilient**: Robust error handling ensures processing continues even if extraction fails

## Usage Example

```python
# In scheduler service
submission_requirements = await self.document_title_extraction_service.extract_submission_requirements(
    opportunity_id=str(item.opps_id),
    tenant_id=str(item.tenant_id),
    source=str(item.opps_source) or "custom"
)
```
